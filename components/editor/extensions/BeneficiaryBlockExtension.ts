import { Node, mergeAttributes } from '@tiptap/core';
import { VueNodeViewRenderer } from '@tiptap/vue-3';
import BeneficiaryBlockComponent from '../BeneficiaryBlockComponent.vue';

export default Node.create({
  name: 'beneficiaryBlock',
  
  group: 'block',
  
  content: '',
  
  atom: true,
  
  addAttributes() {
    return {
      beneficiaryId: {
        default: null,
      },
    };
  },
  
  parseHTML() {
    return [
      {
        tag: 'div[data-type="beneficiary-block"]',
      },
    ];
  },
  
  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(HTMLAttributes, { 'data-type': 'beneficiary-block' }), 0];
  },
  
  addNodeView() {
    return VueNodeViewRenderer(BeneficiaryBlockComponent);
  },
});