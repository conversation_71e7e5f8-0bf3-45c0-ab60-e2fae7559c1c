import { Node, mergeAttributes } from '@tiptap/core';
import { VueNodeViewRenderer } from '@tiptap/vue-3';
import AssetBlockComponent from '../AssetBlockComponent.vue';

export default Node.create({
  name: 'assetBlock',
  
  group: 'block',
  
  content: '',
  
  atom: true,
  
  addAttributes() {
    return {
      assetId: {
        default: null,
      },
    };
  },
  
  parseHTML() {
    return [
      {
        tag: 'div[data-type="asset-block"]',
      },
    ];
  },
  
  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(HTMLAttributes, { 'data-type': 'asset-block' }), 0];
  },
  
  addNodeView() {
    return VueNodeViewRenderer(AssetBlockComponent);
  },
});