<template>
  <div>
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold">Beneficiaries</h1>
        <p class="text-base-content/70">Man<PERSON> who will inherit your assets</p>
      </div>
      
      <div class="flex space-x-2">
        <NuxtLink to="/dashboard/beneficiaries/new" class="btn btn-sm btn-primary">
          <Icon icon="mdi:plus" class="h-4 w-4 mr-1" />
          Add Beneficiary
        </NuxtLink>
      </div>
    </div>
    
    <!-- Digital Executor Info -->
    <div v-if="beneficiaryStore.digitalExecutor" class="alert alert-info mb-6">
      <Icon icon="mdi:shield-crown" class="h-5 w-5" />
      <div>
        <h3 class="font-bold">Digital Executor Assigned</h3>
        <div class="text-sm">
          <strong>{{ beneficiaryStore.digitalExecutor.name }}</strong> is designated as your Digital Executor and will manage your digital assets.
        </div>
      </div>
    </div>
    
    <div v-else class="alert alert-warning mb-6">
      <Icon icon="mdi:alert-triangle" class="h-5 w-5" />
      <div>
        <h3 class="font-bold">No Digital Executor Assigned</h3>
        <div class="text-sm">
          Consider designating one of your beneficiaries as your Digital Executor to manage your digital assets.
        </div>
      </div>
    </div>
    
    <!-- Loading State -->
    <div v-if="beneficiaryStore.isLoading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg"></span>
    </div>
    
    <!-- Beneficiaries Cards -->
    <div v-else-if="beneficiaryStore.beneficiaries.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
      <div v-for="beneficiary in beneficiaryStore.beneficiaries" :key="beneficiary.id" 
           class="card bg-base-100 shadow-md hover:shadow-lg transition-all"
           :class="{ 
             'border-2 border-amber-400 bg-base-300': beneficiary.isDigitalExecutor,
             'border border-base-300': !beneficiary.isDigitalExecutor 
           }">
        <div class="card-body p-4">
          <div class="flex justify-between items-start">
            <div class="flex items-center">
              <div class="avatar mr-3 relative">
                <div class="w-12 h-12 rounded-full bg-primary text-white flex items-center justify-center text-xl font-medium"
                     :class="{ 'bg-amber-500': beneficiary.isDigitalExecutor }">
                  {{ getInitials(beneficiary.name) }}
                </div>
                <!-- Digital Executor Crown Icon -->
                <div v-if="beneficiary.isDigitalExecutor" 
                     class="absolute -top-1 -right-1 bg-base-100 rounded-full p-1">
                  <Icon name="mdi:crown" class="h-3 w-3 text-amber-800" />
                </div>
              </div>
              <div>
                <div class="flex items-center gap-2">
                  <h3 class="font-semibold text-lg">{{ beneficiary.name }}</h3>
                  <div v-if="beneficiary.isDigitalExecutor" class="badge badge-warning badge-sm">
                    <Icon icon="mdi:shield-crown" class="h-3 w-3 mr-1" />
                    Digital Executor
                  </div>
                </div>
                <p class="text-sm text-base-content/70">{{ beneficiary.relationship || 'Beneficiary' }}</p>
              </div>
            </div>
            
            <div class="dropdown dropdown-end">
              <label tabindex="0" class="btn btn-ghost btn-sm btn-circle">
                <Icon icon="mdi:dots-vertical" class="w-5 h-5" />
              </label>
              <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-64">
                <li><a @click="editBeneficiary(beneficiary.id)">Edit Details</a></li>
                <li><a @click="manageAssets(beneficiary.id)">Manage Assets</a></li>
                <li class="divider"></li>
                <li v-if="!beneficiary.isDigitalExecutor">
                  <a @click="setAsDigitalExecutor(beneficiary.id)" class="text-amber-600">
                    <Icon icon="mdi:shield-crown" class="h-4 w-4 mr-2" />
                    Set as Digital Executor
                  </a>
                </li>
                <li v-else>
                  <a @click="removeAsDigitalExecutor(beneficiary.id)" class="text-amber-600">
                    <Icon icon="mdi:crown-off" class="h-4 w-4 mr-2" />
                    Remove Digital Executor
                  </a>
                </li>
                <li class="divider"></li>
                <li><a @click="confirmDeleteBeneficiary(beneficiary.id)" class="text-error">Remove Beneficiary</a></li>
              </ul>
            </div>
          </div>
          
          <div class="divider my-2"></div>
          
          <div class="flex flex-col space-y-3">
            <div class="flex items-center text-sm">
              <Icon name="mdi:email" class="h-4 w-4 mr-2" />
              <span class="truncate">{{ beneficiary.email }}</span>
            </div>
            
            <div v-if="beneficiary.phoneNumber" class="flex items-center text-sm">
              <Icon icon="mdi:phone" class="h-4 w-4 mr-2" />
              <span>{{ beneficiary.phoneNumber }}</span>
            </div>
            
            <div class="flex items-center text-sm">
              <Icon icon="mdi:calendar" class="h-4 w-4 mr-2" />
              <span>Added {{ formatDate(beneficiary.createdAt) }}</span>
            </div>
          </div>
          
          <div class="mt-4">
            <div class="flex items-center justify-between">
              <span class="text-sm font-medium">Access Level:</span>
              <span class="badge" :class="getAccessLevelBadgeClass(beneficiary.accessLevel)">
                {{ formatAccessLevel(beneficiary.accessLevel) }}
              </span>
            </div>
            
            <div class="mt-3">
              <div class="text-sm font-medium mb-1">Assigned Assets ({{ getAssignedAssetCount(beneficiary) }})</div>
              <div v-if="getAssignedAssetCount(beneficiary) > 0" class="flex flex-wrap gap-1">
                <div 
                  v-for="asset in getTopAssignedAssets(beneficiary, 3)" 
                  :key="asset.assetId"
                  class="badge badge-outline"
                >
                  {{ getAssetName(asset.assetId) }}
                </div>
                <div v-if="getAssignedAssetCount(beneficiary) > 3" class="badge badge-outline">
                  +{{ getAssignedAssetCount(beneficiary) - 3 }} more
                </div>
              </div>
              <div v-else class="text-xs text-base-content/60">
                No assets assigned
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Empty State -->
    <div v-else class="bg-base-100 rounded-lg p-8 text-center">
      <div class="mb-4 flex justify-center">
        <div class="rounded-full bg-primary/10 p-3">
          <Icon icon="mdi:account-group" class="h-12 w-12 text-primary" />
        </div>
      </div>
      <h3 class="text-xl font-medium mb-2">No beneficiaries added yet</h3>
      <p class="text-base-content/70 mb-6">
        Start adding people who will inherit your assets
      </p>
      <NuxtLink to="/dashboard/beneficiaries/new" class="btn btn-primary">
        Add Your First Beneficiary
      </NuxtLink>
    </div>
    
    <!-- Delete Confirmation Modal -->
    <div class="modal" :class="{ 'modal-open': showDeleteModal }">
      <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">Confirm Delete</h3>
        <p>Are you sure you want to remove this beneficiary? Any asset assignments will need to be reassigned.</p>
        <div class="modal-action">
          <button @click="showDeleteModal = false" class="btn">Cancel</button>
          <button @click="deleteBeneficiary" class="btn btn-error" :class="{ 'loading': beneficiaryStore.isLoading }">
            Remove
          </button>
        </div>
      </div>
      <div class="modal-backdrop" @click="showDeleteModal = false"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Icon } from '@iconify/vue';
import { useBeneficiaryStore } from '~/stores/beneficiaryStore';
import { useAssetStore } from '~/stores/assetStore';
import type { Beneficiary, AccessLevel, AssetType } from '~/types';

definePageMeta({
  layout: 'dashboard'
});

const beneficiaryStore = useBeneficiaryStore();
const assetStore = useAssetStore();

// Modals
const showDeleteModal = ref(false);
const beneficiaryToDelete = ref<string | null>(null);

// Helper functions
const getInitials = (name: string) => {
  return name
    .split(' ')
    .map(part => part.charAt(0))
    .join('')
    .toUpperCase();
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date);
};

const formatAccessLevel = (level: AccessLevel) => {
  const formatMap: Record<AccessLevel, string> = {
    FULL: 'Full Access',
    READ_ONLY: 'Read Only',
    LIMITED: 'Limited'
  };
  
  return formatMap[level] || level;
};

const getAccessLevelBadgeClass = (level: AccessLevel) => {
  const classMap: Record<AccessLevel, string> = {
    FULL: 'badge-primary',
    READ_ONLY: 'badge-secondary',
    LIMITED: 'badge-accent'
  };
  
  return classMap[level] || '';
};

const getAssignedAssetCount = (beneficiary: Beneficiary) => {
  return beneficiary.assets?.length || 0;
};

const getTopAssignedAssets = (beneficiary: Beneficiary, count: number) => {
  return (beneficiary.assets || []).slice(0, count);
};

const getAssetName = (assetId: string) => {
  const asset = assetStore.assets.find(a => a.id === assetId);
  return asset?.name || 'Unknown Asset';
};

// Actions
const editBeneficiary = (id: string) => {
  navigateTo(`/dashboard/beneficiaries/edit/${id}`);
};

const manageAssets = (id: string) => {
  navigateTo(`/dashboard/beneficiaries/${id}/assets`);
};

const setAsDigitalExecutor = async (id: string) => {
  const beneficiary = beneficiaryStore.beneficiaries.find(b => b.id === id);
  if (!beneficiary) return;
  
  const confirmed = confirm(`Are you sure you want to set ${beneficiary.name} as your Digital Executor? This will remove the Digital Executor status from any other beneficiary.`);
  
  if (confirmed) {
    const success = await beneficiaryStore.setDigitalExecutor(id);
    if (success) {
      // Show success notification
      const notification = document.createElement('div');
      notification.className = 'toast toast-top toast-end';
      notification.innerHTML = `
        <div class="alert alert-success">
          <span>${beneficiary.name} is now your Digital Executor!</span>
        </div>
      `;
      document.body.appendChild(notification);
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 3000);
    }
  }
};

const removeAsDigitalExecutor = async (id: string) => {
  const beneficiary = beneficiaryStore.beneficiaries.find(b => b.id === id);
  if (!beneficiary) return;
  
  const confirmed = confirm(`Are you sure you want to remove ${beneficiary.name} as your Digital Executor?`);
  
  if (confirmed) {
    const success = await beneficiaryStore.removeDigitalExecutor(id);
    if (success) {
      // Show success notification
      const notification = document.createElement('div');
      notification.className = 'toast toast-top toast-end';
      notification.innerHTML = `
        <div class="alert alert-info">
          <span>${beneficiary.name} is no longer your Digital Executor.</span>
        </div>
      `;
      document.body.appendChild(notification);
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 3000);
    }
  }
};

const confirmDeleteBeneficiary = (id: string) => {
  beneficiaryToDelete.value = id;
  showDeleteModal.value = true;
};

const deleteBeneficiary = async () => {
  if (beneficiaryToDelete.value) {
    const success = await beneficiaryStore.deleteBeneficiary(beneficiaryToDelete.value);
    if (success) {
      showDeleteModal.value = false;
      beneficiaryToDelete.value = null;
    }
  }
};

// Fetch data on mount
onMounted(async () => {
  await beneficiaryStore.fetchBeneficiaries();
  await assetStore.fetchAssets();
});
</script>